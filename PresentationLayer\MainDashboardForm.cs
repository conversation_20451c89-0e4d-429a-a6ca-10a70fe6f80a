using DXApplication1.Utilities;
using DXApplication1.Models;
using System.Drawing;
using System.Windows.Forms;

namespace DXApplication1.PresentationLayer
{
    /// <summary>
    /// نموذج لوحة التحكم الرئيسية - Main Dashboard Form
    /// </summary>
    public partial class MainDashboardForm : Form
    {
        private User? _currentUser;

        public MainDashboardForm(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            SetupForm();
            SetupButtons();
            LoadDashboardData();
        }

        private void SetupForm()
        {
            // إعداد النموذج للغة العربية - Setup form for Arabic
            this.Text = $"نظام إدارة المبيعات - مرحباً {_currentUser?.FullName}";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
        }

        private void SetupButtons()
        {
            // إعداد الأزرار حسب صلاحيات المستخدم - Setup buttons based on user permissions
            if (_currentUser?.Role == null) return;

            // إخفاء جميع الأزرار أولاً - Hide all buttons first
            btnCustomers.Visible = _currentUser.Role.CanManageCustomers;
            btnProducts.Visible = _currentUser.Role.CanManageProducts;
            btnInvoices.Visible = _currentUser.Role.CanCreateInvoices;
            btnReports.Visible = _currentUser.Role.CanViewReports;
            btnUsers.Visible = _currentUser.Role.CanManageUsers;
            btnSettings.Visible = _currentUser.Role.CanManageSettings;
        }

        private void LoadDashboardData()
        {
            try
            {
                // تحميل بيانات لوحة التحكم - Load dashboard data
                LoadSummaryData();
                LoadRecentActivities();
                LoadChartData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSummaryData()
        {
            // TODO: Load summary statistics
            // عدد العملاء، المنتجات، الفواتير، إجمالي المبيعات
            lblTotalSales.Text = "إجمالي المبيعات: 0 ر.س";
            lblTotalCustomers.Text = "عدد العملاء: 0";
            lblTotalProducts.Text = "عدد المنتجات: 0";
            lblTotalInvoices.Text = "عدد الفواتير: 0";
        }

        private void LoadRecentActivities()
        {
            // TODO: Load recent activities
            // آخر الفواتير، آخر العملاء المضافين، إلخ
        }

        private void LoadChartData()
        {
            // TODO: Load chart data
            // مخططات المبيعات، أداء المنتجات، إلخ
        }

        // معالجات الأحداث - Event Handlers
        private void btnCustomers_Click(object sender, EventArgs e)
        {
            // فتح نموذج إدارة العملاء - Open customers management form
            var customersForm = new CustomersForm();
            customersForm.ShowDialog();
        }

        private void btnProducts_Click(object sender, EventArgs e)
        {
            // فتح نموذج إدارة المنتجات - Open products management form
            var productsForm = new ProductsForm();
            productsForm.ShowDialog();
        }

        private void btnInvoices_Click(object sender, EventArgs e)
        {
            // فتح نموذج إدارة الفواتير - Open invoices management form
            var invoicesForm = new InvoicesForm();
            invoicesForm.ShowDialog();
        }

        private void btnReports_Click(object sender, EventArgs e)
        {
            // فتح نموذج التقارير - Open reports form
            var reportsForm = new ReportsForm();
            reportsForm.ShowDialog();
        }

        private void btnUsers_Click(object sender, EventArgs e)
        {
            // فتح نموذج إدارة المستخدمين - Open users management form
            var usersForm = new UsersForm();
            usersForm.ShowDialog();
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            // فتح نموذج الإعدادات - Open settings form
            var settingsForm = new SettingsForm();
            settingsForm.ShowDialog();
        }

        private void MainDashboardForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق النظام؟", "تأكيد الإغلاق",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
            {
                e.Cancel = true;
            }
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDashboardData();
        }
    }

    // نماذج وهمية للاختبار - Dummy forms for testing
    public class CustomersForm : Form
    {
        public CustomersForm()
        {
            this.Text = "إدارة العملاء";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class ProductsForm : Form
    {
        public ProductsForm()
        {
            this.Text = "إدارة المنتجات";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class InvoicesForm : Form
    {
        public InvoicesForm()
        {
            this.Text = "إدارة الفواتير";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class ReportsForm : Form
    {
        public ReportsForm()
        {
            this.Text = "التقارير";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class UsersForm : Form
    {
        public UsersForm()
        {
            this.Text = "إدارة المستخدمين";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class SettingsForm : Form
    {
        public SettingsForm()
        {
            this.Text = "الإعدادات";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }
}
