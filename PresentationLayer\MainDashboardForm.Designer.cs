using System.Drawing;
using System.Windows.Forms;

namespace DXApplication1.PresentationLayer
{
    partial class MainDashboardForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelTop = new Panel();
            this.lblWelcome = new Label();
            this.btnLogout = new Button();
            this.btnRefresh = new Button();
            this.panelLeft = new Panel();
            this.btnCustomers = new Button();
            this.btnProducts = new Button();
            this.btnInvoices = new Button();
            this.btnReports = new Button();
            this.btnUsers = new Button();
            this.btnSettings = new Button();
            this.panelMain = new Panel();
            this.panelStats = new Panel();
            this.lblTotalSales = new Label();
            this.lblTotalCustomers = new Label();
            this.lblTotalProducts = new Label();
            this.lblTotalInvoices = new Label();
            this.panelTop.SuspendLayout();
            this.panelLeft.SuspendLayout();
            this.panelMain.SuspendLayout();
            this.panelStats.SuspendLayout();
            this.SuspendLayout();
            //
            // panelTop
            //
            this.panelTop.Controls.Add(this.btnRefresh);
            this.panelTop.Controls.Add(this.btnLogout);
            this.panelTop.Controls.Add(this.lblWelcome);
            this.panelTop.Dock = DockStyle.Top;
            this.panelTop.Location = new Point(0, 0);
            this.panelTop.Name = "panelTop";
            this.panelTop.Size = new Size(1200, 60);
            this.panelTop.TabIndex = 1;
            this.panelTop.BackColor = Color.LightBlue;
            //
            // panelLeft
            //
            this.panelLeft.Controls.Add(this.btnSettings);
            this.panelLeft.Controls.Add(this.btnUsers);
            this.panelLeft.Controls.Add(this.btnReports);
            this.panelLeft.Controls.Add(this.btnInvoices);
            this.panelLeft.Controls.Add(this.btnProducts);
            this.panelLeft.Controls.Add(this.btnCustomers);
            this.panelLeft.Dock = DockStyle.Left;
            this.panelLeft.Location = new Point(0, 60);
            this.panelLeft.Name = "panelLeft";
            this.panelLeft.Size = new Size(200, 600);
            this.panelLeft.TabIndex = 2;
            this.panelLeft.BackColor = Color.LightGray;
            //
            // lblWelcome
            //
            this.lblWelcome.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.lblWelcome.Location = new Point(20, 20);
            this.lblWelcome.Name = "lblWelcome";
            this.lblWelcome.Size = new Size(300, 23);
            this.lblWelcome.TabIndex = 0;
            this.lblWelcome.Text = "نظام إدارة المبيعات";
            //
            // btnLogout
            //
            this.btnLogout.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            this.btnLogout.Font = new Font("Tahoma", 10F);
            this.btnLogout.Location = new Point(1100, 15);
            this.btnLogout.Name = "btnLogout";
            this.btnLogout.Size = new Size(90, 30);
            this.btnLogout.TabIndex = 1;
            this.btnLogout.Text = "تسجيل الخروج";
            this.btnLogout.UseVisualStyleBackColor = true;
            this.btnLogout.Click += new EventHandler(this.btnLogout_Click);
            //
            // btnRefresh
            //
            this.btnRefresh.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            this.btnRefresh.Font = new Font("Tahoma", 10F);
            this.btnRefresh.Location = new Point(1000, 15);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new Size(90, 30);
            this.btnRefresh.TabIndex = 2;
            this.btnRefresh.Text = "تحديث";
            this.btnRefresh.UseVisualStyleBackColor = true;
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);
            //
            // btnCustomers
            //
            this.btnCustomers.Font = new Font("Tahoma", 10F);
            this.btnCustomers.Location = new Point(10, 10);
            this.btnCustomers.Name = "btnCustomers";
            this.btnCustomers.Size = new Size(180, 40);
            this.btnCustomers.TabIndex = 0;
            this.btnCustomers.Text = "العملاء";
            this.btnCustomers.UseVisualStyleBackColor = true;
            this.btnCustomers.Click += new EventHandler(this.btnCustomers_Click);
            //
            // btnProducts
            //
            this.btnProducts.Font = new Font("Tahoma", 10F);
            this.btnProducts.Location = new Point(10, 60);
            this.btnProducts.Name = "btnProducts";
            this.btnProducts.Size = new Size(180, 40);
            this.btnProducts.TabIndex = 1;
            this.btnProducts.Text = "المنتجات";
            this.btnProducts.UseVisualStyleBackColor = true;
            this.btnProducts.Click += new EventHandler(this.btnProducts_Click);
            //
            // btnInvoices
            //
            this.btnInvoices.Font = new Font("Tahoma", 10F);
            this.btnInvoices.Location = new Point(10, 110);
            this.btnInvoices.Name = "btnInvoices";
            this.btnInvoices.Size = new Size(180, 40);
            this.btnInvoices.TabIndex = 2;
            this.btnInvoices.Text = "الفواتير";
            this.btnInvoices.UseVisualStyleBackColor = true;
            this.btnInvoices.Click += new EventHandler(this.btnInvoices_Click);
            //
            // btnReports
            //
            this.btnReports.Font = new Font("Tahoma", 10F);
            this.btnReports.Location = new Point(10, 160);
            this.btnReports.Name = "btnReports";
            this.btnReports.Size = new Size(180, 40);
            this.btnReports.TabIndex = 3;
            this.btnReports.Text = "التقارير";
            this.btnReports.UseVisualStyleBackColor = true;
            this.btnReports.Click += new EventHandler(this.btnReports_Click);
            //
            // btnUsers
            //
            this.btnUsers.Font = new Font("Tahoma", 10F);
            this.btnUsers.Location = new Point(10, 210);
            this.btnUsers.Name = "btnUsers";
            this.btnUsers.Size = new Size(180, 40);
            this.btnUsers.TabIndex = 4;
            this.btnUsers.Text = "المستخدمين";
            this.btnUsers.UseVisualStyleBackColor = true;
            this.btnUsers.Click += new EventHandler(this.btnUsers_Click);
            //
            // btnSettings
            //
            this.btnSettings.Font = new Font("Tahoma", 10F);
            this.btnSettings.Location = new Point(10, 260);
            this.btnSettings.Name = "btnSettings";
            this.btnSettings.Size = new Size(180, 40);
            this.btnSettings.TabIndex = 5;
            this.btnSettings.Text = "الإعدادات";
            this.btnSettings.UseVisualStyleBackColor = true;
            this.btnSettings.Click += new EventHandler(this.btnSettings_Click);
            //
            // panelMain
            //
            this.panelMain.Controls.Add(this.panelStats);
            this.panelMain.Dock = DockStyle.Fill;
            this.panelMain.Location = new Point(200, 60);
            this.panelMain.Name = "panelMain";
            this.panelMain.Size = new Size(1000, 600);
            this.panelMain.TabIndex = 3;
            //
            // panelStats
            //
            this.panelStats.Controls.Add(this.lblTotalInvoices);
            this.panelStats.Controls.Add(this.lblTotalProducts);
            this.panelStats.Controls.Add(this.lblTotalCustomers);
            this.panelStats.Controls.Add(this.lblTotalSales);
            this.panelStats.Dock = DockStyle.Bottom;
            this.panelStats.Location = new Point(0, 400);
            this.panelStats.Name = "panelStats";
            this.panelStats.Size = new Size(1000, 200);
            this.panelStats.TabIndex = 0;
            this.panelStats.BackColor = Color.WhiteSmoke;
            //
            // lblTotalSales
            //
            this.lblTotalSales.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblTotalSales.Location = new Point(20, 20);
            this.lblTotalSales.Name = "lblTotalSales";
            this.lblTotalSales.Size = new Size(200, 19);
            this.lblTotalSales.TabIndex = 0;
            this.lblTotalSales.Text = "إجمالي المبيعات: 0 ر.س";
            //
            // lblTotalCustomers
            //
            this.lblTotalCustomers.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblTotalCustomers.Location = new Point(20, 50);
            this.lblTotalCustomers.Name = "lblTotalCustomers";
            this.lblTotalCustomers.Size = new Size(200, 19);
            this.lblTotalCustomers.TabIndex = 1;
            this.lblTotalCustomers.Text = "عدد العملاء: 0";
            //
            // lblTotalProducts
            //
            this.lblTotalProducts.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblTotalProducts.Location = new Point(20, 80);
            this.lblTotalProducts.Name = "lblTotalProducts";
            this.lblTotalProducts.Size = new Size(200, 19);
            this.lblTotalProducts.TabIndex = 2;
            this.lblTotalProducts.Text = "عدد المنتجات: 0";
            //
            // lblTotalInvoices
            //
            this.lblTotalInvoices.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblTotalInvoices.Location = new Point(20, 110);
            this.lblTotalInvoices.Name = "lblTotalInvoices";
            this.lblTotalInvoices.Size = new Size(200, 19);
            this.lblTotalInvoices.TabIndex = 3;
            this.lblTotalInvoices.Text = "عدد الفواتير: 0";
            //
            // MainDashboardForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 660);
            this.Controls.Add(this.panelMain);
            this.Controls.Add(this.panelLeft);
            this.Controls.Add(this.panelTop);
            this.Name = "MainDashboardForm";
            this.Text = "نظام إدارة المبيعات";
            this.FormClosing += new FormClosingEventHandler(this.MainDashboardForm_FormClosing);
            this.panelTop.ResumeLayout(false);
            this.panelLeft.ResumeLayout(false);
            this.panelMain.ResumeLayout(false);
            this.panelStats.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        #endregion

        private Panel panelTop;
        private Label lblWelcome;
        private Button btnLogout;
        private Button btnRefresh;
        private Panel panelLeft;
        private Button btnCustomers;
        private Button btnProducts;
        private Button btnInvoices;
        private Button btnReports;
        private Button btnUsers;
        private Button btnSettings;
        private Panel panelMain;
        private Panel panelStats;
        private Label lblTotalSales;
        private Label lblTotalCustomers;
        private Label lblTotalProducts;
        private Label lblTotalInvoices;
    }
}
